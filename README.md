# ATMA API Gateway

API Gateway adalah satu-satunya titik masuk (Single Entry Point) untuk semua permintaan dari aplikasi klien ke microservices ATMA Backend.

## 🎯 Tanggung Jawab Utama

- **Routing & Proxying**: Meneruskan permintaan HTTP ke microservice yang tepat
- **Otentikasi & Otorisasi**: Memverifikasi JWT token untuk endpoint yang dilindungi
- **Rate Limiting**: Mencegah penyalahgunaan API dengan pembatasan request
- **Logging Terpusat**: Mencatat semua permintaan untuk debugging dan audit
- **CORS Management**: Mengelola kebijakan Cross-Origin Resource Sharing

## 🏗️ Arsitektur

```
Frontend Apps → API Gateway → Microservices
                    ↓
              [Auth, Rate Limit, Logging]
                    ↓
    [Auth Service, Assessment Service, Archive Service, Admin Service]
```

## 📋 API Endpoints

### Native Endpoints
- `GET /health` - Health check untuk monitoring
- `GET /api` - Informasi API dan endpoint yang tersedia

### Proxy Routes
- `POST /api/auth/*` → Auth Service (login, register)
- `GET|POST /api/assessments/*` → Assessment Service (🔒 User JWT required)
- `GET /api/profiles/*` → Archive Service (🔒 User JWT required)
- `POST /api/admin/auth/*` → Admin Service (admin login)
- `GET|POST /api/admin/*` → Admin Service (🔒 Admin JWT required)

## 🚀 Quick Start

### 1. Environment Setup
```bash
# Copy environment template
cp .env.example .env

# Edit environment variables
nano .env
```

### 2. Development
```bash
# Install dependencies (already done)
npm install

# Start development server
npm run dev
```

### 3. Production
```bash
# Start production server
npm run prod
```

### 4. Docker
```bash
# Build image
docker build -t atma-api-gateway .

# Run container
docker run -p 80:80 --env-file .env atma-api-gateway
```

## ⚙️ Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NODE_ENV` | Environment mode | `development` |
| `PORT` | Server port | `80` |
| `JWT_SECRET_USER` | JWT secret for user tokens | Required |
| `JWT_SECRET_ADMIN` | JWT secret for admin tokens | Required |
| `AUTH_SERVICE_URL` | Auth service URL | `http://auth-service:3001` |
| `ASSESSMENT_SERVICE_URL` | Assessment service URL | `http://assessment-service:3002` |
| `ARCHIVE_SERVICE_URL` | Archive service URL | `http://archive-service:3003` |
| `ADMIN_SERVICE_URL` | Admin service URL | `http://admin-service:3004` |
| `FRONTEND_URL` | Frontend URL for CORS | `http://localhost:3000` |
| `RATE_LIMIT_MAX` | Max requests per 15 minutes | `100` |

### JWT Token Format

**User Token:**
```json
{
  "sub": "user_id",
  "email": "<EMAIL>",
  "iat": **********,
  "exp": **********
}
```

**Admin Token:**
```json
{
  "sub": "admin_id",
  "email": "<EMAIL>",
  "role": "admin",
  "iat": **********,
  "exp": **********
}
```

## 🔒 Security Features

- **JWT Validation**: Automatic token verification for protected routes
- **Rate Limiting**: 100 requests per 15 minutes per IP
- **CORS Protection**: Configurable origin whitelist
- **Security Headers**: XSS protection, clickjacking prevention
- **Input Validation**: Request size limits and sanitization

## 📊 Monitoring & Logging

### Health Check
```bash
curl http://localhost:80/health
```

### Log Format
```
[timestamp] METHOD /path STATUS response-time user-type/user-id remote-addr "user-agent"
```

### Error Handling
- Automatic service unavailable responses for downstream failures
- Structured error responses with appropriate HTTP status codes
- Development vs production error detail levels

## 🐳 Docker Integration

### Dockerfile Features
- Multi-stage build for optimization
- Non-root user for security
- Health check integration
- Graceful shutdown handling

### Docker Compose
```yaml
api-gateway:
  build: .
  ports:
    - "80:80"
  environment:
    - NODE_ENV=production
    - JWT_SECRET_USER=${JWT_SECRET_USER}
    - JWT_SECRET_ADMIN=${JWT_SECRET_ADMIN}
  depends_on:
    - auth-service
    - assessment-service
    - archive-service
    - admin-service
```

## 🔧 Development

### Project Structure
```
src/
├── middleware/
│   ├── auth.js          # JWT validation middleware
│   └── logging.js       # Morgan logging configuration
├── routes/
│   └── index.js         # Route definitions and proxy setup
├── app.js               # Express app configuration
└── index.js             # Server entry point
```

### Adding New Routes
1. Add service URL to environment variables
2. Configure proxy in `src/routes/index.js`
3. Add authentication middleware if needed
4. Update documentation

## 🚨 Troubleshooting

### Common Issues

**Service Unavailable (503)**
- Check if target microservice is running
- Verify service URLs in environment variables
- Check Docker network connectivity

**Unauthorized (401)**
- Verify JWT secret matches between services
- Check token format and expiration
- Ensure Authorization header format: `Bearer <token>`

**CORS Errors**
- Add frontend URL to `FRONTEND_URL` environment variable
- Check CORS configuration in `src/app.js`

### Debug Mode
```bash
NODE_ENV=development npm start
```

## � Service Integration Guide

### Bagaimana Microservices Berinteraksi dengan API Gateway

API Gateway bertindak sebagai **reverse proxy** yang meneruskan request dari client ke microservice yang tepat. Setiap microservice harus mengimplementasikan endpoint yang sesuai dengan routing yang dikonfigurasi di gateway.

### 🔐 Auth Service Integration

**Endpoint yang Diproxy:**
- `POST /api/auth/*` → Auth Service

**Yang Sudah Dilakukan Gateway:**
- ✅ Meneruskan semua request ke `/api/auth/*` tanpa autentikasi
- ✅ Menangani error jika auth service tidak tersedia (503)
- ✅ Logging semua request untuk audit

**Yang Diharapkan dari Auth Service:**
```bash
# Auth service harus menyediakan endpoint:
POST /api/auth/login          # Login pengguna
POST /api/auth/register       # Registrasi pengguna
POST /api/auth/refresh        # Refresh token
POST /api/auth/logout         # Logout pengguna
GET  /api/auth/verify         # Verifikasi token
```

**Format Response yang Diharapkan:**
```json
// Login/Register Success
{
  "success": true,
  "data": {
    "user": {
      "id": "user_id",
      "email": "<EMAIL>"
    },
    "token": "jwt_token_here"
  }
}

// Error Response
{
  "success": false,
  "error": "Error message",
  "message": "Detailed error description"
}
```

**JWT Token yang Harus Dihasilkan:**
- **Secret:** Menggunakan `JWT_SECRET_USER` dari environment
- **Algorithm:** HS256
- **Payload:** `{ "sub": "user_id", "email": "<EMAIL>", "iat": timestamp, "exp": timestamp }`

### 📊 Assessment Service Integration

**Endpoint yang Diproxy:**
- `GET|POST /api/assessments/*` → Assessment Service (🔒 **Memerlukan User JWT**)

**Yang Sudah Dilakukan Gateway:**
- ✅ Validasi JWT token pengguna sebelum meneruskan request
- ✅ Menambahkan header `X-User-ID` dan `X-User-Email` ke request
- ✅ Menangani error jika assessment service tidak tersedia (503)
- ✅ Logging dengan informasi user untuk audit

**Yang Diharapkan dari Assessment Service:**
```bash
# Assessment service harus menyediakan endpoint:
GET  /api/assessments                    # List assessments untuk user
POST /api/assessments                    # Buat assessment baru
GET  /api/assessments/:id               # Detail assessment
POST /api/assessments/:id/submit        # Submit jawaban assessment
GET  /api/assessments/:id/results       # Hasil assessment
```

**Headers yang Diterima dari Gateway:**
```http
X-User-ID: user_id_from_jwt
X-User-Email: user_email_from_jwt
Authorization: Bearer jwt_token
Content-Type: application/json
```

**Cara Menggunakan Headers:**
```javascript
// Di assessment service
app.use('/api/assessments', (req, res, next) => {
  const userId = req.headers['x-user-id'];
  const userEmail = req.headers['x-user-email'];

  // Gunakan userId untuk filter data per user
  req.currentUser = { id: userId, email: userEmail };
  next();
});
```

### 📁 Archive Service Integration

**Endpoint yang Diproxy:**
- `GET /api/profiles/*` → Archive Service (🔒 **Memerlukan User JWT**)

**Yang Sudah Dilakukan Gateway:**
- ✅ Validasi JWT token pengguna sebelum meneruskan request
- ✅ Menambahkan header `X-User-ID` dan `X-User-Email` ke request
- ✅ Menangani error jika archive service tidak tersedia (503)
- ✅ Logging dengan informasi user untuk audit

**Yang Diharapkan dari Archive Service:**
```bash
# Archive service harus menyediakan endpoint:
GET  /api/profiles                      # Profile pengguna
GET  /api/profiles/assessments          # History assessment pengguna
GET  /api/profiles/results              # Semua hasil assessment pengguna
GET  /api/profiles/results/{id}         # Detail hasil assessment spesifik
GET  /api/profiles/certificates         # Sertifikat yang diperoleh
GET  /api/profiles/certificates/{id}    # Detail sertifikat spesifik
PUT  /api/profiles                      # Update profile pengguna
POST /api/profiles/results              # Simpan hasil assessment baru (dari assessment-service)
```

**Headers yang Diterima dari Gateway:**
```http
X-User-ID: user_id_from_jwt
X-User-Email: user_email_from_jwt
Authorization: Bearer jwt_token
Content-Type: application/json
```

**Contoh Response yang Diharapkan:**

*GET /api/profiles/assessments:*
```json
{
  "success": true,
  "data": [
    {
      "id": "assessment_123",
      "title": "RIASEC Career Assessment",
      "type": "RIASEC",
      "completedAt": "2024-01-15T10:30:00Z",
      "status": "completed",
      "overallScore": 85
    }
  ]
}
```

*GET /api/profiles/results/{id}:*
```json
{
  "success": true,
  "data": {
    "assessmentId": "assessment_123",
    "title": "RIASEC Career Assessment",
    "completedAt": "2024-01-15T10:30:00Z",
    "results": {
      "RIASEC": {
        "Realistic": 75,
        "Investigative": 85,
        "Artistic": 60,
        "Social": 90,
        "Enterprising": 70,
        "Conventional": 65
      },
      "recommendations": ["Counselor", "Teacher"],
      "strengths": ["Communication", "Empathy"]
    }
  }
}
```

### 🛡️ Security & Authentication Flow

**1. Request Tanpa Autentikasi (Auth Service):**
```
Client → API Gateway → Auth Service
         [No JWT Check]
```

**2. Request dengan Autentikasi User:**
```
Client → API Gateway → [JWT Validation] → Assessment/Archive Service
         [Add X-User-* Headers]
```

**3. Request dengan Autentikasi Admin:**
```
Client → API Gateway → [Admin JWT Validation] → Admin Service
         [Add X-Admin-* Headers]
```

### 📋 Service Requirements Checklist

**Untuk semua microservices:**
- [ ] Endpoint harus tersedia di port yang dikonfigurasi (3001, 3002, 3003, 3004)
- [ ] Harus menangani request dengan path prefix yang benar (`/api/auth`, `/api/assessments`, `/api/profiles`)
- [ ] Harus mengembalikan response JSON yang valid
- [ ] Harus menangani error dengan status code HTTP yang tepat
- [ ] Harus dapat diakses dari API Gateway (network connectivity)

**Untuk Auth Service:**
- [ ] Menggunakan JWT secret yang sama dengan gateway (`JWT_SECRET_USER`, `JWT_SECRET_ADMIN`)
- [ ] Menghasilkan JWT dengan format payload yang benar
- [ ] Endpoint login/register/refresh tersedia

**Untuk Assessment & Archive Service:**
- [ ] Membaca dan menggunakan header `X-User-ID` dan `X-User-Email`
- [ ] Memfilter data berdasarkan user yang sedang login
- [ ] Tidak mengakses database user langsung (stateless)

### 🔧 Development Setup untuk Services

**1. Environment Variables yang Dibutuhkan:**
```bash
# Di setiap microservice
JWT_SECRET_USER=same_as_gateway_secret
JWT_SECRET_ADMIN=same_as_gateway_secret
DATABASE_URL=your_database_connection
PORT=3001  # atau 3002, 3003, 3004 sesuai service
```

**2. Testing Connectivity:**
```bash
# Test dari API Gateway ke service
curl http://localhost:3001/api/auth/health
curl http://localhost:3002/api/assessments/health
curl http://localhost:3003/api/profiles/health
```

**3. Docker Network Setup:**
```yaml
# docker-compose.yml
services:
  api-gateway:
    depends_on:
      - auth-service
      - assessment-service
      - archive-service

  auth-service:
    networks:
      - atma-network

  assessment-service:
    networks:
      - atma-network

networks:
  atma-network:
    driver: bridge
```

## �📝 License

MIT License - see LICENSE file for details.
